/* support-page.css
   样式基于 download-page.css，专用于服务与支持页面。 */
:root {
    --primary-blue: #0066CC;
    --primary-light-blue: #E6F0F9;
    --text-primary: #333333;
    --text-secondary: #666666;
    --border-color: #E5E5E5;
    --background-light: #F8F8F8;
    --white: #FFFFFF;
}

/* 只在body上添加overflow-x: hidden，防止横向滚动条 */
body {
    overflow-x: hidden;
}

/* 页面标题背景板 */
.page-title-banner {
    width: 100%;
    height: 6.25vw; /* 120px */
    background: #F8F8F8;
    border-radius: 0;
    display: flex;
    align-items: center;
    margin-bottom: 2.5vw; /* 48px */
}

.page-title {
    font-family: "Source Han Sans CN", "Microsoft YaHei", sans-serif;
    font-weight: 500;
    font-size: 2.29vw; /* 44px */
    color: #00509E;
    line-height: 4.43vw; /* 85px */
    text-align: center;
    font-style: normal;
    text-transform: none;
    margin: 0;
}

/* 修复container样式冲突 */
.page-title-banner .container {
    margin: 0 auto; /* 添加自动边距使内容居中 */
}

/* 主要内容区域 */
.support-main {
    display: flex;
    padding: 1.041667vw; /* 20px */
    gap: 1.041667vw; /* 20px */
    max-width: 62.5vw; /* 1200px */
    margin: 0 auto;
    align-items: stretch; /* 让子元素高度一致 */
}

/* 左侧选择区域 */
.support-sidebar {
    width: 12.5vw; /* 240px */
    display: flex;
    flex-direction: column;
    gap: 0.520833vw; /* 10px */
}

/* 菜单样式 - 完全按照资料下载页面设计 */
.support-sidebar ul {
    background: #f7f7f7;
    border-radius: 0.625vw 0 0 0.625vw; /* 12px 0 0 12px */
    box-shadow: none;
    margin: 0;
    padding: 0;
}

/* 单一菜单框展开效果 */
.expandable-menu {
    width: 12.5vw; /* 240px */
    border-radius: 0.625vw; /* 12px */
    margin-bottom: 0.3125vw; /* 6px */
    border: 0.104167vw solid #646464; /* 1.5px */
    background: #F8F8F8;
    transition: all 0.3s ease;
    overflow: visible; /* 改为visible */
    height: auto; /* 改为auto */
    min-height: 3.072917vw; /* 添加最小高度 */
}

.expandable-menu:hover {
    border-color: #0050A2;
}

/* 展开时的高度和边框颜色 */
.expandable-menu.expanded {
    border-color: #0050A2;
    height: auto;
    padding-bottom: 0.78125vw; /* 15px */
}

/* 菜单标题 */
.menu-title {
    height:3.072917vw; /* 59px */
    display: flex;
    align-items: center;
    padding-left: 1.458333vw; /* 28px */
    font-family: "思源黑体";
    font-size: 1.041667vw; /* 20px */
    font-weight: normal;
    color: #646464;
    position: relative;
    cursor: pointer;
}

.expandable-menu:hover .menu-title {
    color: #0050A2;
}

.expandable-menu.expanded .menu-title {
    color: #0050A2;
}

/* 箭头图标 */
.arrow-icon {
    position: absolute;
    right: 1.51042vw; /* 29px */
    top: 50%;
    transform: translateY(-50%);
    transition: transform 0.3s ease;
    pointer-events: none; /* 防止箭头捕获点击事件 */
    width: 0.833333vw; /* 16px */
    height: 0.416667vw; /* 8px */
}

.expandable-menu.expanded .arrow-icon {
    /* 展开时旋转180度，图标由JavaScript控制 */
    transform: translateY(-50%) rotate(180deg);
}

/* 分隔线 - 在同一个文本框内 */
.menu-divider {
    width: 10.0vw; /* 192px (240px - 48px padding) */
    height: 0;
    border-top: 1px solid #0050A2; /* 2px */
    margin: 0 0 0.677083vw 1.302083vw; /* 13px 25px */
    display: none; /* 默认隐藏 */
}
/* 展开时显示分割线 */
.expandable-menu.expanded .menu-divider {
    display: block;
}

/* 菜单项容器 */
.menu-items {
    display: flex;
    flex-direction: column;
    max-height: none; /* 移除最大高度限制 */
    overflow: visible; /* 确保内容可见 */
}

/* 菜单项 */
.menu-item {
    padding: 0 0 0 1.458333vw; /* 0 0 0 28px */
    font-family: "思源黑体";
    font-size: 0.833333vw; /* 16px */
    font-weight: normal;
    line-height: 33px;
    letter-spacing: normal;
    color: #646464;
    cursor: pointer;
    transition: color 0.3s ease;
}

/* 英文界面下菜单项字体大小调整 */
html[lang="en"] .menu-item {
    font-size: 0.625vw; /* 12px，进一步缩小字号适应英文长度 */
    letter-spacing: -0.015625vw; /* -0.3px，稍微调整字间距 */
}

.menu-item a {
    display: block;
    color: #646464;
    text-decoration: none;
    transition: color 0.3s ease;
}

.menu-item:hover, .menu-item.active {
    color: #0050A2;
}

.menu-item:hover a, .menu-item.active a {
    color: #0050A2;
}

.menu-item:not(:last-child) {
    margin-bottom: -0.260417vw; /* -5px */
}

/* 右侧内容区域 */
.support-content {
    flex: 1;
    background: #ffffff;
    border-radius: 0 0.625vw 0.625vw 0; /* 0 12px 12px 0 */
    padding: 0 1.041667vw 1.041667vw 1.041667vw; /* 顶部0，左右下20px - 让顶部与左侧菜单对齐 */
    height: 100%; /* 占满父容器高度，与左侧菜单栏保持一致 */
    min-height: 500px; /* 设置最小高度 */
    display: flex;
    flex-direction: column;
}



/* 支持列表相关样式 */
.support-container {
    display: flex;
    gap: 1.71875vw; /* 33px */
    justify-content: center;
    margin: 0 auto;
    width: 90vw; /* 扩大容器宽度，让内容能够充分利用空间 */
    max-width: 1400px; /* 设置最大宽度 */
    overflow: hidden;
    margin-bottom: 5.546875vw; /* 106.5px */
    flex: 1; /* 让支持容器占满剩余空间 */
    align-items: flex-start; /* 内容从顶部开始对齐 */
}

.support-column {
    overflow: hidden;
}

/* 左侧栏占用剩余空间 */
#left-column {
    flex: 1; /* 占用剩余空间 */
    min-width: 0; /* 允许收缩 */
}

/* 右侧栏保持固定宽度 */
#right-column {
    width: 19.895833vw; /* 382px - 保持右侧栏固定宽度 */
    flex-shrink: 0; /* 不允许收缩 */
}

/* 新增布局相关样式 */
.support-section {
    margin: 0;
    padding: 0;
    position: relative; /* 添加相对定位 */
}

.support-section:after {
    content: '';
    display: block;
    clear: both;
}

.section-group {
    width: 100%;
    margin-bottom: 1.041667vw; /* 20px */
}

.section-title {
    font-family: "思源黑体";
    font-size: 1.041667vw; /* 20px */
    font-weight: normal;
    line-height: normal;
    letter-spacing: normal;
    color: #323232;
    margin-top: 1.875vw; /* 36px */
    margin-bottom: -0.416667vw; /* -8px */
}

.support-item {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 0.807292vw; /* 15.5px */
    position: relative;
    width: 100%;
}

.support-item::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    height: 0;
    border-top: 0.052083vw solid #CCCCCC; /* 1px */
}

.support-item:last-child {
    border-bottom: none;
}

.item-info {
    flex: 1;
    min-width: 0;
    width: 100%;
    max-width: 18.385417vw; /* 353px */
    margin-left: -0.822917vw; /* -15.8px */
    word-wrap: break-word;
    overflow-wrap: break-word;
}

.item-name {
    font-family: "思源黑体";
    font-size: 0.833333vw; /* 16px */
    font-weight: normal;
    line-height: normal;
    letter-spacing: normal;
    color: #007DDB;
}

.item-note {
    font-family: "思源黑体";
    font-size: 0.729167vw; /* 14px */
    font-weight: normal;
    line-height: normal;
    letter-spacing: normal;
    color: #929292;
    text-align: left;
    max-width: 15.729167vw; /* 302px */
    white-space: pre-wrap;  /* 保留空格和换行 */
    word-wrap: break-word;  /* 确保长词能够换行 */
}

.support-action {
    display: flex;
    flex-direction: column;
    align-items: center;
    cursor: pointer;
    transition: all 0.3s ease;
}

.support-action img {
    width: 1.041667vw; /* 20px */
    height: 1.041667vw; /* 20px */
    margin-bottom: 0.104167vw; /* 2px */
    transition: all 0.3s ease;
    content: var(--support-icon);
}

.support-action span {
    font-size: 0.729167vw; /* 14px */
    color: #929292;
    transition: color 0.3s ease;
}

/* 悬停效果 - 使用蓝色版本的SVG */
.support-action:hover img {
    content: var(--support-icon-hover);
}

.support-action:hover span {
    color: #0050A2;
}

/* 支持列表布局 */
.support-sections {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    align-items: start;
}

.support-list {
    width: 100%;
}

/* 面包屑导航样式 */
.breadcrumb {
    margin: 0;
    padding: 0;
    background: none;
    display: flex;
    align-items: center;
    font-family: "Source Han Sans CN", "Microsoft YaHei", sans-serif;
    font-weight: 400;
    font-size: 0.83vw; /* 16px */
    line-height: 1.51vw; /* 29px */
    font-style: normal;
    text-transform: none;
    margin-bottom: 1.458333vw; /* 28px */
    margin-left: 1.09375vw;  /* 21px */
}

.breadcrumb a {
    color: #333333;
    text-decoration: none;
}

.breadcrumb a:hover {
    color: #055999;
}

.breadcrumb .separator {
    margin: 0 0.42vw; /* 8px */
    color: #333333;
}

.breadcrumb .current {
    color: #333333;
}

/* 页脚间距 */
footer {
    margin-top: 5.208333vw; /* 100px */
    position: relative;
    clear: both;
}
.containert {
    margin-left: 17.760417vw; /* 341px */
    margin-right: 19.791667vw; /* 380px */
}

/* 支持按钮容器 */
.support-buttons {
    display: flex;
    gap: 0.833333vw;  /* 16px */
    margin-top: 1.041667vw;  /* 20px */
}

/* 支持按钮样式 */
.support-btn {
    height: 2.708333vw; /* 52px */
    padding: 0 1.666667vw; /* 32px */
    border-radius: 0.416667vw; /* 8px */
    font-family: "思源黑体";
    font-size: 1.041667vw; /* 20px */
    font-weight: normal;
    line-height: 2.708333vw; /* 52px */
    text-align: center;
    letter-spacing: normal;
    color: #FFFFFF;
    background: #0050A2;
    text-decoration: none;
    transition: background-color 0.3s ease;
}

.support-btn:hover {
    background: #007DDB;
    color: #FFFFFF;
    text-decoration: none;
}

/* 针对491px以下屏幕的媒体查询 */
@media screen and (max-width: 491px) {
    /* 基础样式 */
    body {
        font-size: 6px; /* 原来20px的0.3倍 */
    }

    /* 页面标题背景板 */
    .page-title-banner {
        height: 36px; /* 120px的0.3倍 */
        margin-bottom: 14.4px; /* 48px的0.3倍 */
    }

    .page-title {
        font-size: 13.2px; /* 44px的0.3倍 */
        line-height: 25.5px; /* 85px的0.3倍 */
    }

    /* 主要内容区域 */
    .support-main {
        flex-direction: column;
        max-width: 360px; /* 1200px的0.3倍 */
        margin: 0 auto;
        padding: 6px; /* 20px的0.3倍 */
        gap: 6px; /* 20px的0.3倍 */
        align-items: stretch; /* 保持子元素拉伸 */
    }

    /* 左侧选择区域 */
    .support-sidebar {
        width: 72px; /* 240px的0.3倍 */
        gap: 3px; /* 10px的0.3倍 */
    }

    .sidebar-category {
        margin-bottom: 1.8px; /* 6px的0.3倍 */
    }

    .category-select {
        width: 87.9px; /* 293px的0.3倍 */
        height: 18.9px; /* 63px的0.3倍 */
        border-radius: 3.6px; /* 12px的0.3倍 */
        border-width: 0.45px; /* 1.5px的0.3倍 */
        font-size: 6px; /* 20px的0.3倍 */
        line-height: 6px; /* 20px的0.3倍 */
        padding: 0 0 0 9.6px; /* 32px的0.3倍 */
        background-position: right 8.25px center; /* 27.48px的0.3倍 */
        background-size: 4.8px 4.8px; /* 16px的0.3倍 */
    }

    /* 单一菜单框 */
    .expandable-menu {
        width: 87.9px; /* 293px的0.3倍 */
        border-radius: 3.6px; /* 12px的0.3倍 */
        margin-bottom: 1.8px; /* 6px的0.3倍 */
        border-width: 0.45px; /* 1.5px的0.3倍 */
        height: 18.9px; /* 63px的0.3倍 */
    }

    .expandable-menu.expanded {
        padding-bottom: 5.7px; /* 19px的0.3倍 */
    }

    /* 菜单标题 */
    .menu-title {
        height: 18.9px; /* 63px的0.3倍 */
        padding-left: 9.6px; /* 32px的0.3倍 */
        font-size: 6px; /* 20px的0.3倍 */
    }

    /* 箭头图标 */
    .arrow-icon {
        right: 8.25px; /* 27.48px的0.3倍 */
        width: 4.8px; /* 16px的0.3倍 */
        height: 4.8px; /* 16px的0.3倍 */
    }

    /* 分隔线 */
    .menu-divider {
        width: 70.2px; /* 234px的0.3倍 */
        border-top-width: 0.45px; /* 1.5px的0.3倍 */
        margin: 0 0 4.5px 9.45px; /* 15px和31.5px的0.3倍 */
    }

    /* 菜单项 */
    .menu-item {
        padding: 2.4px 0 2.4px 9.6px; /* 8px 0 8px 32px的0.3倍 */
        font-size: 4.8px; /* 16px的0.3倍 */
    }

    /* 移动端英文界面下菜单项字体大小调整 */
    html[lang="en"] .menu-item {
        font-size: 3.6px; /* 12px的0.3倍，进一步缩小适应英文 */
        letter-spacing: -0.09px; /* -0.3px的0.3倍 */
    }

    .menu-item:not(:last-child) {
        margin-bottom: -1.5px; /* -5px的0.3倍 */
    }

    /* 右侧内容区域 */
    .support-content {
        flex: 1;
        border-radius: 0 3.6px 3.6px 0; /* 0 12px 12px 0 的0.3倍 */
        padding: 0 6px 6px 6px; /* 顶部0，左右下6px - 移动端也让顶部与左侧菜单对齐 */
        min-height: 150px; /* 移动端最小高度 */
        display: flex;
        flex-direction: column;
    }



    /* 支持列表相关样式 */
    .support-container {
        gap: 9.9px; /* 33px的0.3倍 */
        width: 90vw; /* 使用视口宽度 */
        max-width: 360px; /* 移动端最大宽度 */
        margin-bottom: 31.95px; /* 106.5px的0.3倍 */
    }

    .support-column {
        overflow: hidden;
    }

    /* 移动端左侧栏占用剩余空间 */
    #left-column {
        flex: 1;
        min-width: 0;
    }

    /* 移动端右侧栏保持固定宽度 */
    #right-column {
        width: 114.6px; /* 382px的0.3倍 */
        flex-shrink: 0;
    }

    .section-group {
        margin-bottom: 6px; /* 20px的0.3倍 */
    }

    .section-title {
        font-size: 6px; /* 20px的0.3倍 */
        margin-top: 10.8px; /* 36px的0.3倍 */
        margin-bottom: -2.4px; /* -8px的0.3倍 */
    }

    .support-item {
        margin-bottom: 4.65px; /* 15.5px的0.3倍 */
    }

    .support-item::after {
        border-top-width: 0.3px; /* 1px的0.3倍 */
    }

    .item-info {
        max-width: none;
        margin-left: -2px;
        width: calc(100% - 20px);
        position: relative;
    }

    .item-name {
        font-size: 8px !important;
        transform: scale(0.7);
        transform-origin: left top;
        display: inline-block; /* 使用inline-block而不是block以维持在一行 */
        line-height: 1; /* 使用最小的行高 */
        margin-bottom: 3px; /* 添加底部边距以确保与下方内容分开 */
    }

    .item-note {
        font-size: 8px !important;
        transform: scale(0.7);
        transform-origin: left top;
        display: block;
        line-height: 1;
        width: 350%;
        word-break: break-all;
        word-wrap: break-word;
        white-space: pre-wrap;
    }

    .support-action img {
        width: 6px; /* 20px的0.3倍 */
        height: 6px; /* 20px的0.3倍 */
        margin-bottom: 0.6px; /* 2px的0.3倍 */
    }

    .support-action span {
        font-size: 4.2px; /* 14px的0.3倍 */
    }

    /* 支持按钮 */
    .support-btn {
        height: 15.6px; /* 52px的0.3倍 */
        padding: 0 9.6px; /* 32px的0.3倍 */
        border-radius: 2.4px; /* 8px的0.3倍 */
        font-size: 6px; /* 20px的0.3倍 */
        line-height: 15.6px; /* 52px的0.3倍 */
    }

    /* 面包屑导航 */
    .breadcrumb {
        margin-bottom: 8.4px; /* 28px的0.3倍 */
        margin-left: 6.3px; /* 21px的0.3倍 */
        font-size: 4.8px; /* 16px的0.3倍 */
        line-height: 8.7px; /* 29px的0.3倍 */
    }

    .breadcrumb .separator {
        margin: 0 2.4px; /* 8px的0.3倍 */
    }

    /* 页脚间距 */
    footer {
        margin-top: 30px; /* 100px的0.3倍 */
    }

    .containert {
        margin-left: 0px; /* 341px的0.3倍 */
        margin-right: 0px; /* 380px的0.3倍 */
    }

    /* 支持按钮容器 */
    .support-buttons {
        gap: 4.8px; /* 16px的0.3倍 */
        margin-top: 6px; /* 20px的0.3倍 */
    }

    /* 英文界面下载项详情内容的文字样式 */
    html[lang="en"] .item-name,
    html[lang="en"] .item-note,
    html[lang="en"] .item-desc,
    html[lang="en"] .support-item .version-number,
    html[lang="en"] .support-item .extract-code {
        font-size: 8px !important;
        transform: scale(0.7);
        transform-origin: left top;
        display: block; /* 改为block以允许换行 */
        line-height: 1; /* 使用最小的行高 */
    }

}
#support-content > *:first-child {
    margin-top: 0 !important;
}
